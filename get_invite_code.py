#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邀请码图片链接获取工具
支持跨语言调用，返回 JSON 格式结果
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import json
import sys
import logging
import os
from typing import Dict, Optional

# 抑制 Selenium 和 Chrome 的调试信息
os.environ['WDM_LOG_LEVEL'] = '0'  # 抑制 WebDriver Manager 日志
logging.getLogger('selenium').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)


def setup_chrome_driver() -> webdriver.Chrome:
    """
    配置并启动 Chrome 浏览器

    Returns:
        webdriver.Chrome: 配置好的 Chrome 驱动实例
    """
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式（不显示浏览器窗口）
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")  # 设置窗口大小
    chrome_options.add_argument("--no-sandbox")  # Linux 可能需要
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-logging")  # 禁用日志输出
    chrome_options.add_argument("--log-level=3")  # 只显示致命错误
    chrome_options.add_argument("--silent")  # 静默模式
    chrome_options.add_argument("--disable-extensions")  # 禁用扩展
    chrome_options.add_argument("--disable-web-security")  # 禁用网络安全检查
    chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

    # 抑制 Chrome 的调试信息输出
    chrome_options.add_experimental_option('excludeSwitches', ['enable-logging'])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    return webdriver.Chrome(options=chrome_options)


def get_invite_code_image_url(url: str = "https://linux.do/t/topic/545650", timeout: int = 10) -> Dict[str, any]:
    """
    获取邀请码图片链接
    
    Args:
        url (str): 目标网页 URL
        timeout (int): 等待超时时间（秒）
    
    Returns:
        Dict[str, any]: 包含结果的字典
            - success (bool): 是否成功
            - img_url (str): 图片链接（成功时）
            - title (str): 页面标题（成功时）
            - error (str): 错误信息（失败时）
    """
    driver = None
    result = {
        "success": False,
        "img_url": None,
        "title": None,
        "error": None
    }
    
    try:
        # 启动浏览器
        driver = setup_chrome_driver()
        
        # 访问目标 URL
        driver.get(url)
        
        # 等待页面加载（显式等待）
        WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((By.XPATH, '//p[@dir="auto"][contains(., "当前注册邀请码")]'))
        )
        
        # 等待并获取包含"当前注册邀请码"的<p>元素
        invite_code_element = WebDriverWait(driver, timeout).until(
            EC.presence_of_element_located((By.XPATH, '//p[@dir="auto"][contains(., "当前注册邀请码")]'))
        )
        
        # 获取<p>元素内的<img>标签的src属性
        img_element = invite_code_element.find_element(By.TAG_NAME, "img")
        img_url = img_element.get_attribute("src")
        
        # 获取页面标题
        page_title = driver.title
        
        # 更新结果
        result.update({
            "success": True,
            "img_url": img_url,
            "title": page_title
        })
        
    except Exception as e:
        result["error"] = str(e)
        logging.error(f"获取邀请码图片链接失败: {e}")
        
    finally:
        # 确保浏览器被关闭
        if driver:
            try:
                driver.quit()
            except Exception as e:
                logging.warning(f"关闭浏览器时出错: {e}")
    
    return result


def main():
    """
    主函数 - 命令行调用入口
    输出 JSON 格式结果，便于其他语言解析
    """
    # 抑制所有日志输出，避免干扰 JSON 输出
    logging.basicConfig(level=logging.CRITICAL)

    # 重定向 stderr 到 devnull，避免 Chrome 调试信息干扰输出
    devnull = open(os.devnull, 'w')
    old_stderr = sys.stderr
    sys.stderr = devnull

    try:
        # 获取邀请码图片链接
        result = get_invite_code_image_url()

        # 恢复 stderr
        sys.stderr = old_stderr
        devnull.close()

        # 输出纯净的 JSON 格式结果（确保 ASCII 编码）
        print(json.dumps(result, ensure_ascii=True, indent=2))

        # 设置退出码
        sys.exit(0 if result["success"] else 1)

    except KeyboardInterrupt:
        sys.stderr = old_stderr
        devnull.close()
        print(json.dumps({"success": False, "error": "User interrupted"}, ensure_ascii=True))
        sys.exit(1)
    except Exception as e:
        sys.stderr = old_stderr
        devnull.close()
        print(json.dumps({"success": False, "error": f"Unknown error: {str(e)}"}, ensure_ascii=True))
        sys.exit(1)


if __name__ == "__main__":
    main()
