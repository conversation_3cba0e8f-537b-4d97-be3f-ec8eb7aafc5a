from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

# 配置 Chrome 无头模式（可选）

chrome_options = Options()
chrome_options.add_argument("--headless")  # 无头模式（不显示浏览器窗口）
chrome_options.add_argument("--disable-gpu")
chrome_options.add_argument("--window-size=1920,1080")  # 设置窗口大小
chrome_options.add_argument("--no-sandbox")  # Linux 可能需要
chrome_options.add_argument("--disable-dev-shm-usage")
chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

# 启动浏览器
driver = webdriver.Chrome(options=chrome_options)  # 如果 ChromeDriver 不在 PATH，需指定 executable_path

try:
    # 访问目标 URL
    url = "https://linux.do/t/topic/545650"
    driver.get(url)

    # 等待页面加载（显式等待）
    WebDriverWait(driver, 10).until(
        # EC.presence_of_element_located((By.CSS_SELECTOR, ".topic-body"))
        EC.presence_of_element_located((By.XPATH, '//p[@dir="auto"][contains(., "当前注册邀请码")]'))
    )
    # 等待并获取包含"当前注册邀请码"的<p>元素
    invite_code_element = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.XPATH, '//p[@dir="auto"][contains(., "当前注册邀请码")]'))
    )

    # 获取<p>元素内的<img>标签的src属性
    img_element = invite_code_element.find_element(By.TAG_NAME, "img")
    img_url = img_element.get_attribute("src")
    
    print("邀请码图片链接:", img_url)
    # 获取页面标题
    print("页面标题:", driver.title)


finally:
    # 关闭浏览器
    driver.quit()